-- Sam<PERSON> Lu<PERSON> for Testing Obfuscation

-- Simple Hello World
print("Hello, World!")

-- Function with variables
function greet(name)
    local message = "Hello, " .. name .. "!"
    print(message)
    return message
end

greet("User")

-- Loop example
for i = 1, 5 do
    print("Count: " .. i)
end

-- Table operations
local myTable = {
    name = "<PERSON>",
    age = 30,
    city = "New York"
}

for key, value in pairs(myTable) do
    print(key .. ": " .. value)
end

-- Conditional logic
local number = 42
if number > 50 then
    print("Number is greater than 50")
elseif number == 42 then
    print("The answer to everything!")
else
    print("Number is less than or equal to 50")
end

-- String manipulation
local text = "This is a test string"
local words = {}
for word in text:gmatch("%S+") do
    table.insert(words, word)
end

print("Words found: " .. #words)

-- Math operations
local function calculate(a, b)
    local sum = a + b
    local product = a * b
    local difference = a - b
    
    return {
        sum = sum,
        product = product,
        difference = difference
    }
end

local result = calculate(10, 5)
print("Sum: " .. result.sum)
print("Product: " .. result.product)
print("Difference: " .. result.difference)
