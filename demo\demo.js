const LuaObfuscator = require('../src/obfuscator');

async function runDemo() {
    console.log('🎯 Lua Script Obfuscator - Live Demo\n');
    
    const obfuscator = new LuaObfuscator();
    
    // Demo script
    const originalScript = `
-- Simple calculator function
function calculate(operation, a, b)
    if operation == "add" then
        return a + b
    elseif operation == "subtract" then
        return a - b
    elseif operation == "multiply" then
        return a * b
    elseif operation == "divide" then
        if b ~= 0 then
            return a / b
        else
            return "Error: Division by zero"
        end
    else
        return "Error: Unknown operation"
    end
end

-- Test the calculator
print("Calculator Demo:")
print("5 + 3 =", calculate("add", 5, 3))
print("10 - 4 =", calculate("subtract", 10, 4))
print("6 * 7 =", calculate("multiply", 6, 7))
print("15 / 3 =", calculate("divide", 15, 3))
print("10 / 0 =", calculate("divide", 10, 0))
`;

    console.log('📝 Original Lua Script:');
    console.log('─'.repeat(50));
    console.log(originalScript);
    console.log('─'.repeat(50));
    console.log(`Original size: ${originalScript.length} characters\n`);
    
    console.log('🔄 Obfuscating script...\n');
    
    try {
        const startTime = Date.now();
        const obfuscatedScript = await obfuscator.obfuscate(originalScript);
        const endTime = Date.now();
        
        console.log('✅ Obfuscation completed successfully!\n');
        
        console.log('📊 Statistics:');
        console.log(`• Original size: ${originalScript.length} characters`);
        console.log(`• Obfuscated size: ${obfuscatedScript.length} characters`);
        console.log(`• Size increase: ${(obfuscatedScript.length / originalScript.length).toFixed(2)}x`);
        console.log(`• Processing time: ${endTime - startTime}ms\n`);
        
        console.log('🔒 Obfuscated Lua Script (first 500 characters):');
        console.log('─'.repeat(50));
        console.log(obfuscatedScript.substring(0, 500) + '...');
        console.log('─'.repeat(50));
        
        console.log('\n🎉 Demo completed! The obfuscated script maintains full functionality');
        console.log('   while being extremely difficult to reverse engineer.');
        
    } catch (error) {
        console.error('❌ Obfuscation failed:', error.message);
    }
}

// Run the demo
runDemo().catch(console.error);
