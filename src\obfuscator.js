const crypto = require('crypto');
const stripComments = require('strip-comments');

class LuaObfuscator {
    constructor() {
        this.variableNames = this.generateVariableNames();
        this.functionNames = this.generateFunctionNames();
    }

    generateVariableNames() {
        const names = [];
        const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        for (let i = 0; i < 100; i++) {
            let name = '';
            for (let j = 0; j < Math.floor(Math.random() * 8) + 3; j++) {
                name += chars[Math.floor(Math.random() * chars.length)];
            }
            names.push(name);
        }
        return names;
    }

    generateFunctionNames() {
        const names = [];
        const prefixes = ['func', 'proc', 'exec', 'run', 'call', 'invoke', 'handle', 'process'];
        for (let i = 0; i < 50; i++) {
            const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
            const suffix = Math.random().toString(36).substring(2, 8);
            names.push(prefix + suffix);
        }
        return names;
    }

    generateRandomKey(length = 32) {
        return crypto.randomBytes(length).toString('hex');
    }

    xorEncrypt(data, key) {
        let result = '';
        for (let i = 0; i < data.length; i++) {
            const charCode = data.charCodeAt(i) ^ key.charCodeAt(i % key.length);
            result += String.fromCharCode(charCode);
        }
        return result;
    }

    base64Encode(str) {
        return Buffer.from(str, 'utf8').toString('base64');
    }

    createVirtualMachine() {
        const vmVar = this.variableNames[0];
        const decodeFunc = this.functionNames[0];
        const executeFunc = this.functionNames[1];
        const keyVar = this.variableNames[1];
        const dataVar = this.variableNames[2];
        
        return `
local ${vmVar} = {}
${vmVar}.${keyVar} = nil
${vmVar}.${dataVar} = nil

function ${vmVar}:${decodeFunc}(${dataVar}, ${keyVar})
    local result = ""
    local keyLen = #${keyVar}
    for i = 1, #${dataVar} do
        local char = string.byte(${dataVar}, i)
        local keyChar = string.byte(${keyVar}, ((i - 1) % keyLen) + 1)
        result = result .. string.char(char ~ keyChar)
    end
    return result
end

function ${vmVar}:${executeFunc}()
    local decoded = self:${decodeFunc}(self.${dataVar}, self.${keyVar})
    local func = load(decoded)
    if func then
        return func()
    else
        error("Failed to load decoded script")
    end
end`;
    }

    obfuscate(luaCode) {
        try {
            // Step 1: Remove comments
            const cleanedCode = stripComments(luaCode, 'lua');
            
            // Step 2: Apply multiple layers of encoding
            const key1 = this.generateRandomKey(16);
            const key2 = this.generateRandomKey(24);
            const key3 = this.generateRandomKey(32);
            
            // Layer 1: Base64 encode
            let encoded = this.base64Encode(cleanedCode);
            
            // Layer 2: XOR with first key
            encoded = this.xorEncrypt(encoded, key1);
            
            // Layer 3: Base64 encode again
            encoded = this.base64Encode(encoded);
            
            // Layer 4: XOR with second key
            encoded = this.xorEncrypt(encoded, key2);
            
            // Layer 5: Final Base64 encoding
            encoded = this.base64Encode(encoded);
            
            // Layer 6: XOR with third key
            const finalEncoded = this.xorEncrypt(encoded, key3);
            
            // Create the virtual machine decoder
            const vm = this.createVirtualMachine();
            
            // Create the obfuscated script
            const obfuscatedScript = this.createObfuscatedScript(vm, finalEncoded, key1, key2, key3);
            
            return obfuscatedScript;
            
        } catch (error) {
            console.error('Obfuscation error:', error);
            throw new Error('Failed to obfuscate script: ' + error.message);
        }
    }

    removeComments(code) {
        // Remove single-line comments
        code = code.replace(/--[^\r\n]*/g, '');
        
        // Remove multi-line comments
        code = code.replace(/--\[\[[\s\S]*?\]\]/g, '');
        
        // Remove extra whitespace
        code = code.replace(/\s+/g, ' ').trim();
        
        return code;
    }

    createObfuscatedScript(vm, encodedData, key1, key2, key3) {
        const vmVar = this.variableNames[0];
        const keyVar1 = this.variableNames[3];
        const keyVar2 = this.variableNames[4];
        const keyVar3 = this.variableNames[5];
        const dataVar = this.variableNames[2];
        const tempVar1 = this.variableNames[6];
        const tempVar2 = this.variableNames[7];
        const tempVar3 = this.variableNames[8];
        const decodeFunc1 = this.functionNames[2];
        const decodeFunc2 = this.functionNames[3];
        const decodeFunc3 = this.functionNames[4];
        
        // Split the encoded data into chunks to make it less obvious
        const chunks = this.splitIntoChunks(encodedData, 50);
        const chunkAssignments = chunks.map((chunk, i) => 
            `local ${tempVar1}_${i} = "${this.escapeString(chunk)}"`
        ).join('\n');
        
        const dataAssignment = chunks.map((_, i) => `${tempVar1}_${i}`).join(' .. ');
        
        return `${vm}

local ${keyVar1} = "${this.escapeString(key1)}"
local ${keyVar2} = "${this.escapeString(key2)}"
local ${keyVar3} = "${this.escapeString(key3)}"

${chunkAssignments}
local ${dataVar} = ${dataAssignment}

local function ${decodeFunc1}(data)
    local result = ""
    for i = 1, #data do
        local byte = string.byte(data, i)
        if byte >= 65 and byte <= 90 then
            result = result .. string.char(((byte - 65 + 26 - 13) % 26) + 65)
        elseif byte >= 97 and byte <= 122 then
            result = result .. string.char(((byte - 97 + 26 - 13) % 26) + 97)
        else
            result = result .. string.char(byte)
        end
    end
    return result
end

local function ${decodeFunc2}(str)
    local data = ""
    str = string.gsub(str, "[^A-Za-z0-9+/]", "")
    str = str .. string.rep("=", (4 - #str % 4) % 4)
    for i = 1, #str, 4 do
        local a, b, c, d = string.byte(str, i, i + 3)
        a = a and ((a >= 65 and a <= 90) and (a - 65) or (a >= 97 and a <= 122) and (a - 71) or (a >= 48 and a <= 57) and (a + 4) or (a == 43) and 62 or 63) or 0
        b = b and ((b >= 65 and b <= 90) and (b - 65) or (b >= 97 and b <= 122) and (b - 71) or (b >= 48 and b <= 57) and (b + 4) or (b == 43) and 62 or 63) or 0
        c = c and ((c >= 65 and c <= 90) and (c - 65) or (c >= 97 and c <= 122) and (c - 71) or (c >= 48 and c <= 57) and (c + 4) or (c == 43) and 62 or 63) or 0
        d = d and ((d >= 65 and d <= 90) and (d - 65) or (d >= 97 and d <= 122) and (d - 71) or (d >= 48 and d <= 57) and (d + 4) or (d == 43) and 62 or 63) or 0
        data = data .. string.char(bit32.bor(bit32.lshift(a, 2), bit32.rshift(b, 4)))
        if c ~= 0 then data = data .. string.char(bit32.bor(bit32.lshift(bit32.band(b, 15), 4), bit32.rshift(c, 2))) end
        if d ~= 0 then data = data .. string.char(bit32.bor(bit32.lshift(bit32.band(c, 3), 6), d)) end
    end
    return data
end

local function ${decodeFunc3}()
    local ${tempVar1} = ${vmVar}:${this.functionNames[0]}(${dataVar}, ${keyVar3})
    local ${tempVar2} = ${decodeFunc2}(${tempVar1})
    local ${tempVar3} = ${vmVar}:${this.functionNames[0]}(${tempVar2}, ${keyVar2})
    local decoded1 = ${decodeFunc2}(${tempVar3})
    local decoded2 = ${vmVar}:${this.functionNames[0]}(decoded1, ${keyVar1})
    local final = ${decodeFunc2}(decoded2)
    local func = load(final)
    if func then func() else error("Execution failed") end
end

${vmVar}.${this.variableNames[2]} = ${dataVar}
${vmVar}.${this.variableNames[1]} = ${keyVar3}

${decodeFunc3}()`;
    }

    splitIntoChunks(str, chunkSize) {
        const chunks = [];
        for (let i = 0; i < str.length; i += chunkSize) {
            chunks.push(str.substring(i, i + chunkSize));
        }
        return chunks;
    }

    escapeString(str) {
        return str.replace(/\\/g, '\\\\').replace(/"/g, '\\"').replace(/\n/g, '\\n').replace(/\r/g, '\\r');
    }
}

module.exports = LuaObfuscator;
