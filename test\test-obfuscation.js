const LuaObfuscator = require('../src/obfuscator');

async function testObfuscation() {
    const obfuscator = new LuaObfuscator();
    
    // Test script 1: Simple Hello World
    const testScript1 = `
-- Simple Hello World
print("Hello, World!")
local name = "User"
print("Hello, " .. name)
`;

    // Test script 2: Function with loops
    const testScript2 = `
-- Function with loops and conditionals
function fibonacci(n)
    if n <= 1 then
        return n
    else
        return fibonacci(n-1) + fibon<PERSON>ci(n-2)
    end
end

for i = 1, 10 do
    print("<PERSON>bonacci(" .. i .. ") = " .. fibonacci(i))
end
`;

    // Test script 3: Complex script with tables
    const testScript3 = `
-- Complex script with tables and string manipulation
local users = {
    {name = "Alice", age = 25, city = "New York"},
    {name = "Bob", age = 30, city = "Los Angeles"},
    {name = "<PERSON>", age = 35, city = "Chicago"}
}

function processUsers(userList)
    local result = {}
    for i, user in ipairs(userList) do
        local info = string.format("%s (%d) from %s", user.name, user.age, user.city)
        table.insert(result, info)
        print("User " .. i .. ": " .. info)
    end
    return result
end

local processedUsers = processUsers(users)
print("Total users processed: " .. #processedUsers)
`;

    console.log('🧪 Testing Lua Obfuscation System\n');
    
    const testCases = [
        { name: 'Simple Hello World', script: testScript1 },
        { name: 'Function with Loops', script: testScript2 },
        { name: 'Complex Script', script: testScript3 }
    ];

    for (let i = 0; i < testCases.length; i++) {
        const testCase = testCases[i];
        console.log(`📝 Test ${i + 1}: ${testCase.name}`);
        console.log(`Original size: ${testCase.script.length} characters`);
        
        try {
            const startTime = Date.now();
            const obfuscated = await obfuscator.obfuscate(testCase.script);
            const endTime = Date.now();
            
            console.log(`✅ Obfuscation successful!`);
            console.log(`Obfuscated size: ${obfuscated.length} characters`);
            console.log(`Obfuscation ratio: ${(obfuscated.length / testCase.script.length).toFixed(2)}x`);
            console.log(`Processing time: ${endTime - startTime}ms`);
            
            // Show a preview of the obfuscated code
            const preview = obfuscated.substring(0, 200) + (obfuscated.length > 200 ? '...' : '');
            console.log(`Preview: ${preview}`);
            
        } catch (error) {
            console.log(`❌ Obfuscation failed: ${error.message}`);
        }
        
        console.log('─'.repeat(60));
    }
}

// Run the test
testObfuscation().catch(console.error);
