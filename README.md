# 🔒 Lua Script Obfuscator

A powerful web application for obfuscating Lua scripts with advanced multi-layer encryption and virtual machine-like decoder structures. This tool transforms readable Lua code into heavily obfuscated versions that are extremely difficult to reverse engineer while maintaining full functionality.

## ✨ Features

- **Multi-Layer Obfuscation**: Applies Base64 encoding, XOR encryption, and additional obfuscation layers
- **Virtual Machine Decoder**: Embeds a VM-like decoder structure within the obfuscated output
- **Advanced Minification**: Removes comments, minifies code, and applies code optimization
- **Web Interface**: User-friendly web interface with real-time statistics
- **Copy & Download**: Easy copying to clipboard and downloading of obfuscated scripts
- **Processing Statistics**: Shows obfuscation ratios, processing time, and size comparisons

## 🚀 Quick Start

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone or download this repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the server:
   ```bash
   npm start
   ```

4. Open your browser and navigate to `http://localhost:3000`

## 🛠️ Usage

### Web Interface

1. **Input**: Paste your Lua script into the input text area
2. **Obfuscate**: Click the "🔐 Obfuscate Script" button
3. **Output**: The obfuscated code will appear in the output area
4. **Copy/Download**: Use the copy button or download the result

### API Endpoint

You can also use the API directly:

```javascript
POST /api/obfuscate
Content-Type: application/json

{
  "code": "print('Hello, World!')"
}
```

Response:
```javascript
{
  "success": true,
  "obfuscatedCode": "...",
  "stats": {
    "originalSize": 23,
    "obfuscatedSize": 1247,
    "processingTime": 15,
    "compressionRatio": "54.22"
  }
}
```

## 🔧 Technical Details

### Obfuscation Process

The obfuscation system applies multiple layers of protection:

1. **Comment Removal**: Strips all comments and unnecessary whitespace
2. **Code Minification**: Optimizes and compresses the code structure
3. **Base64 Encoding**: First layer of encoding
4. **XOR Encryption**: Multiple XOR operations with random keys
5. **Virtual Machine Wrapping**: Embeds the code in a VM-like decoder
6. **Final Minification**: Compresses the entire obfuscated output

### Security Features

- **Random Key Generation**: Uses cryptographically secure random keys
- **Multiple Encryption Layers**: Applies 6+ layers of obfuscation
- **Variable Name Randomization**: Generates random variable and function names
- **String Literal Encoding**: Encodes string literals to prevent easy detection
- **Control Flow Obfuscation**: Makes the execution flow difficult to follow

## 📁 Project Structure

```
lua-obfuscator/
├── public/                 # Frontend files
│   ├── index.html         # Main web interface
│   ├── styles.css         # Styling
│   └── script.js          # Frontend JavaScript
├── src/                   # Backend source code
│   ├── obfuscator.js      # Main obfuscation engine
│   └── minifier.js        # Code minification utilities
├── test/                  # Test files and examples
│   ├── sample-scripts.lua # Sample Lua scripts
│   └── test-obfuscation.js # Test suite
├── server.js              # Express.js server
├── package.json           # Dependencies and scripts
└── README.md             # This file
```

## 🧪 Testing

Run the test suite to verify obfuscation functionality:

```bash
node test/test-obfuscation.js
```

This will test various Lua scripts and show obfuscation statistics.

## 📊 Performance

- **Processing Speed**: Typically processes scripts in 5-50ms
- **Obfuscation Ratio**: Usually 15-50x size increase
- **Memory Usage**: Efficient processing with minimal memory footprint
- **Compatibility**: Works with all standard Lua syntax

## 🔒 Security Considerations

This tool is designed for legitimate purposes such as:
- Protecting intellectual property in Lua scripts
- Educational purposes and security research
- Code obfuscation for competitive programming

**Important**: Always ensure you have the right to obfuscate any code you process.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.

## 📄 License

This project is licensed under the ISC License - see the package.json file for details.

## ⚠️ Disclaimer

This tool is provided for educational and legitimate security purposes only. Users are responsible for ensuring they have the right to obfuscate any code they process and for complying with all applicable laws and regulations.
