{"name": "lua-obfuscator", "version": "1.0.0", "description": "Advanced Lua script obfuscation web application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["lua", "obfuscation", "security", "encryption"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"body-parser": "^2.2.0", "cors": "^2.8.5", "express": "^4.18.2", "js-beautify": "^1.15.4", "strip-comments": "^2.0.1", "terser": "^5.43.1", "uglify-js": "^3.19.3"}}